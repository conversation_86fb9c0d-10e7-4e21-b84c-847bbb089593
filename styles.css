/* ===================== */
/* RESET + GLOBAL STYLES */
/* ===================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background: url('nat.jpg') no-repeat center center fixed;
  background-size: cover;
  color: #333;
}

/* ===================== */
/* ✅ WELCOME PAGE STYLES */
/* ===================== */

/* Top bar with logo and social icons */
.topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 30px;
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
  flex-wrap: wrap;
}

.logo-title {
  display: flex;
  align-items: center;
}

.logo {
  height: 50px;
  margin-right: 10px;
}

.topbar h1 {
  color: #1a3c6c;
  font-size: 28px;
}

.social-icons a {
  color: #1a3c6c;
  margin-left: 15px;
  font-size: 20px;
  text-decoration: none;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: #007bff;
}

/* Hamburger menu icon */
.hamburger {
  display: none;
  font-size: 28px;
  cursor: pointer;
  color: #1a3c6c;
  margin-top: 10px;
}

/* Navigation bar */
nav {
  background-color: #2c2f77;
}

nav ul {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 10px 0;
  flex-wrap: wrap;
}

nav ul li {
  margin: 0 15px;
}

nav ul li a {
  color: white;
  text-decoration: none;
  font-weight: 550;
}

nav ul li.active a {
  color: #00ffff;
  border-bottom: 2px solid #00ffff;
  padding-bottom: 2px;
}

/* Hero Section */
.hero {
  position: relative;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Welcome Text Box */
.overlay-text {
  background-color: rgba(26, 93, 123, 0.9);
  color: white;
  padding: 30px 40px;
  text-align: center;
  font-size: 24px;
  font-family: monospace;
  max-width: 400px;
  border-radius: 5px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.overlay-text h2 {
  line-height: 1.5;
}

/* ===================== */
/* ✅ CONTACT PAGE STYLES */
/* ===================== */

/* Top Header */
.full-header {
  background-color: #2f4f6f;
  color: white;
  text-align: center;
  padding: 20px;
  font-size: 26px;
  font-weight: bold;
  width: 100%;
}

/* Fixed Side Labels */
.vertical-label {
  writing-mode: vertical-rl;
  transform: rotate(180deg);
  background-color: #e6b8da;
  color: #000;
  font-weight: bold;
  font-size: 20px;
  text-align: center;
  padding: 10px;
  width: 90px;
  height: calc(100vh - 60px);
  position: fixed;
  top: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  z-index: 1;
}
.left-label {
  left: 0;
}
.right-label {
  right: 0;
  transform: rotate(360deg);
}

/* Content Container */
.contact-content {
  max-width: 1300px;
  margin: 100px auto 40px;
  padding: 0 20px;
  position: relative;
  z-index: 0;
}

/* Contact Header */
.contact-header {
  text-align: center;
  margin-bottom: 40px;
}

.contact-header h2 {
  font-size: 2.5rem;
  color: #2f4f6f;
  margin-bottom: 10px;
  font-weight: 600;
}

.contact-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact Grid */
.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
  margin-bottom: 50px;
}

/* Contact Cards */
.contact-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e0e0e0;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.contact-card-header {
  background: linear-gradient(135deg, #2f4f6f, #3c6387);
  color: white;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.contact-icon {
  background: rgba(255, 255, 255, 0.2);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.contact-card-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.contact-card-body {
  padding: 25px;
}

.contact-person {
  margin-bottom: 20px;
}

.contact-person:last-child {
  margin-bottom: 0;
}

.contact-person h4 {
  color: #2f4f6f;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.95rem;
}

.contact-item i {
  color: #3c6387;
  width: 16px;
  text-align: center;
}

.contact-item a {
  color: #2f4f6f;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-item a:hover {
  color: #007bff;
  text-decoration: underline;
}

/* Brochure Banner */
.brochure-banner {
  background-color: #2f4f6f;
  color: white;
  text-align: center;
  padding: 20px 60px;
  font-weight: bold;
  font-size: 20px;
  min-width: 400px;
  margin: 0 auto 50px;
  border-radius: 0 0 30px 30px;
}

/* ===================== */
/* ✅ RESPONSIVE STYLES */
/* ===================== */

@media (max-width: 768px) {
  .hamburger {
    display: block;
  }

  nav ul {
    display: none;
    flex-direction: column;
    background-color: #2c2f77;
    width: 100%;
    text-align: center;
  }

  nav ul.show {
    display: flex;
  }

  nav ul li {
    margin: 10px 0;
  }

  .topbar {
    flex-direction: column;
    align-items: flex-start;
  }

  .logo-title {
    justify-content: space-between;
    width: 100%;
  }

  .overlay-text {
    font-size: 18px;
    width: 90%;
  }

  .hero {
    height: auto;
    padding: 40px 0;
  }

  .vertical-label {
    display: none;
  }

  .brochure-banner {
    min-width: auto;
    font-size: 16px;
    padding: 15px;
  }

  .contact-table th,
  .contact-table td {
    font-size: 14px;
    padding: 12px 15px;
  }
}
